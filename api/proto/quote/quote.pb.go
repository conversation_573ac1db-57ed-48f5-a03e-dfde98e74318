// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v4.25.3
// source: proto/quote.proto

package quote

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SubscribeRequest_Action int32

const (
	SubscribeRequest_SUBSCRIBE   SubscribeRequest_Action = 0
	SubscribeRequest_UNSUBSCRIBE SubscribeRequest_Action = 1
)

// Enum value maps for SubscribeRequest_Action.
var (
	SubscribeRequest_Action_name = map[int32]string{
		0: "SUBSCRIBE",
		1: "UNSUBSCRIBE",
	}
	SubscribeRequest_Action_value = map[string]int32{
		"SUBSCRIBE":   0,
		"UNSUBSCRIBE": 1,
	}
)

func (x SubscribeRequest_Action) Enum() *SubscribeRequest_Action {
	p := new(SubscribeRequest_Action)
	*p = x
	return p
}

func (x SubscribeRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscribeRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_quote_proto_enumTypes[0].Descriptor()
}

func (SubscribeRequest_Action) Type() protoreflect.EnumType {
	return &file_proto_quote_proto_enumTypes[0]
}

func (x SubscribeRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscribeRequest_Action.Descriptor instead.
func (SubscribeRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_proto_quote_proto_rawDescGZIP(), []int{2, 0}
}

// Request message for GetQuote
type GetQuoteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQuoteRequest) Reset() {
	*x = GetQuoteRequest{}
	mi := &file_proto_quote_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQuoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuoteRequest) ProtoMessage() {}

func (x *GetQuoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quote_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuoteRequest.ProtoReflect.Descriptor instead.
func (*GetQuoteRequest) Descriptor() ([]byte, []int) {
	return file_proto_quote_proto_rawDescGZIP(), []int{0}
}

func (x *GetQuoteRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

// Request message for Watch
type WatchRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WatchRequest) Reset() {
	*x = WatchRequest{}
	mi := &file_proto_quote_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchRequest) ProtoMessage() {}

func (x *WatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quote_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchRequest.ProtoReflect.Descriptor instead.
func (*WatchRequest) Descriptor() ([]byte, []int) {
	return file_proto_quote_proto_rawDescGZIP(), []int{1}
}

func (x *WatchRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

// Request message for Subscribe stream
type SubscribeRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Action        SubscribeRequest_Action `protobuf:"varint,1,opt,name=action,proto3,enum=quote.SubscribeRequest_Action" json:"action,omitempty"`
	Symbol        string                  `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeRequest) Reset() {
	*x = SubscribeRequest{}
	mi := &file_proto_quote_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeRequest) ProtoMessage() {}

func (x *SubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quote_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeRequest.ProtoReflect.Descriptor instead.
func (*SubscribeRequest) Descriptor() ([]byte, []int) {
	return file_proto_quote_proto_rawDescGZIP(), []int{2}
}

func (x *SubscribeRequest) GetAction() SubscribeRequest_Action {
	if x != nil {
		return x.Action
	}
	return SubscribeRequest_SUBSCRIBE
}

func (x *SubscribeRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

// Quote message
type Quote struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Symbol        string                 `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Price         float64                `protobuf:"fixed64,2,opt,name=price,proto3" json:"price,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Volume        int64                  `protobuf:"varint,4,opt,name=volume,proto3" json:"volume,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Quote) Reset() {
	*x = Quote{}
	mi := &file_proto_quote_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Quote) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Quote) ProtoMessage() {}

func (x *Quote) ProtoReflect() protoreflect.Message {
	mi := &file_proto_quote_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Quote.ProtoReflect.Descriptor instead.
func (*Quote) Descriptor() ([]byte, []int) {
	return file_proto_quote_proto_rawDescGZIP(), []int{3}
}

func (x *Quote) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Quote) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Quote) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Quote) GetVolume() int64 {
	if x != nil {
		return x.Volume
	}
	return 0
}

var File_proto_quote_proto protoreflect.FileDescriptor

const file_proto_quote_proto_rawDesc = "" +
	"\n" +
	"\x11proto/quote.proto\x12\x05quote\x1a\x1fgoogle/protobuf/timestamp.proto\")\n" +
	"\x0fGetQuoteRequest\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\"&\n" +
	"\fWatchRequest\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\"\x8c\x01\n" +
	"\x10SubscribeRequest\x126\n" +
	"\x06action\x18\x01 \x01(\x0e2\x1e.quote.SubscribeRequest.ActionR\x06action\x12\x16\n" +
	"\x06symbol\x18\x02 \x01(\tR\x06symbol\"(\n" +
	"\x06Action\x12\r\n" +
	"\tSUBSCRIBE\x10\x00\x12\x0f\n" +
	"\vUNSUBSCRIBE\x10\x01\"\x87\x01\n" +
	"\x05Quote\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12\x14\n" +
	"\x05price\x18\x02 \x01(\x01R\x05price\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x16\n" +
	"\x06volume\x18\x04 \x01(\x03R\x06volume2\xa6\x01\n" +
	"\fQuoteService\x120\n" +
	"\bGetQuote\x12\x16.quote.GetQuoteRequest\x1a\f.quote.Quote\x12,\n" +
	"\x05Watch\x12\x13.quote.WatchRequest\x1a\f.quote.Quote0\x01\x126\n" +
	"\tSubscribe\x12\x17.quote.SubscribeRequest\x1a\f.quote.Quote(\x010\x01B\x0fZ\r./proto/quoteb\x06proto3"

var (
	file_proto_quote_proto_rawDescOnce sync.Once
	file_proto_quote_proto_rawDescData []byte
)

func file_proto_quote_proto_rawDescGZIP() []byte {
	file_proto_quote_proto_rawDescOnce.Do(func() {
		file_proto_quote_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_quote_proto_rawDesc), len(file_proto_quote_proto_rawDesc)))
	})
	return file_proto_quote_proto_rawDescData
}

var file_proto_quote_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_quote_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proto_quote_proto_goTypes = []any{
	(SubscribeRequest_Action)(0),  // 0: quote.SubscribeRequest.Action
	(*GetQuoteRequest)(nil),       // 1: quote.GetQuoteRequest
	(*WatchRequest)(nil),          // 2: quote.WatchRequest
	(*SubscribeRequest)(nil),      // 3: quote.SubscribeRequest
	(*Quote)(nil),                 // 4: quote.Quote
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_proto_quote_proto_depIdxs = []int32{
	0, // 0: quote.SubscribeRequest.action:type_name -> quote.SubscribeRequest.Action
	5, // 1: quote.Quote.timestamp:type_name -> google.protobuf.Timestamp
	1, // 2: quote.QuoteService.GetQuote:input_type -> quote.GetQuoteRequest
	2, // 3: quote.QuoteService.Watch:input_type -> quote.WatchRequest
	3, // 4: quote.QuoteService.Subscribe:input_type -> quote.SubscribeRequest
	4, // 5: quote.QuoteService.GetQuote:output_type -> quote.Quote
	4, // 6: quote.QuoteService.Watch:output_type -> quote.Quote
	4, // 7: quote.QuoteService.Subscribe:output_type -> quote.Quote
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_proto_quote_proto_init() }
func file_proto_quote_proto_init() {
	if File_proto_quote_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_quote_proto_rawDesc), len(file_proto_quote_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_quote_proto_goTypes,
		DependencyIndexes: file_proto_quote_proto_depIdxs,
		EnumInfos:         file_proto_quote_proto_enumTypes,
		MessageInfos:      file_proto_quote_proto_msgTypes,
	}.Build()
	File_proto_quote_proto = out.File
	file_proto_quote_proto_goTypes = nil
	file_proto_quote_proto_depIdxs = nil
}
