// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.3
// source: proto/quote.proto

package quote

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	QuoteService_GetQuote_FullMethodName  = "/quote.QuoteService/GetQuote"
	QuoteService_Watch_FullMethodName     = "/quote.QuoteService/Watch"
	QuoteService_Subscribe_FullMethodName = "/quote.QuoteService/Subscribe"
)

// QuoteServiceClient is the client API for QuoteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Quote service definition
type QuoteServiceClient interface {
	// Unary RPC: Get current quote for a symbol
	GetQuote(ctx context.Context, in *GetQuoteRequest, opts ...grpc.CallOption) (*Quote, error)
	// Server streaming RPC: Watch price updates for a symbol
	Watch(ctx context.Context, in *WatchRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[Quote], error)
	// Bidirectional streaming RPC: Subscribe/unsubscribe to multiple symbols
	Subscribe(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[SubscribeRequest, Quote], error)
}

type quoteServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewQuoteServiceClient(cc grpc.ClientConnInterface) QuoteServiceClient {
	return &quoteServiceClient{cc}
}

func (c *quoteServiceClient) GetQuote(ctx context.Context, in *GetQuoteRequest, opts ...grpc.CallOption) (*Quote, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Quote)
	err := c.cc.Invoke(ctx, QuoteService_GetQuote_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *quoteServiceClient) Watch(ctx context.Context, in *WatchRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[Quote], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &QuoteService_ServiceDesc.Streams[0], QuoteService_Watch_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[WatchRequest, Quote]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type QuoteService_WatchClient = grpc.ServerStreamingClient[Quote]

func (c *quoteServiceClient) Subscribe(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[SubscribeRequest, Quote], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &QuoteService_ServiceDesc.Streams[1], QuoteService_Subscribe_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[SubscribeRequest, Quote]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type QuoteService_SubscribeClient = grpc.BidiStreamingClient[SubscribeRequest, Quote]

// QuoteServiceServer is the server API for QuoteService service.
// All implementations must embed UnimplementedQuoteServiceServer
// for forward compatibility.
//
// Quote service definition
type QuoteServiceServer interface {
	// Unary RPC: Get current quote for a symbol
	GetQuote(context.Context, *GetQuoteRequest) (*Quote, error)
	// Server streaming RPC: Watch price updates for a symbol
	Watch(*WatchRequest, grpc.ServerStreamingServer[Quote]) error
	// Bidirectional streaming RPC: Subscribe/unsubscribe to multiple symbols
	Subscribe(grpc.BidiStreamingServer[SubscribeRequest, Quote]) error
	mustEmbedUnimplementedQuoteServiceServer()
}

// UnimplementedQuoteServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedQuoteServiceServer struct{}

func (UnimplementedQuoteServiceServer) GetQuote(context.Context, *GetQuoteRequest) (*Quote, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuote not implemented")
}
func (UnimplementedQuoteServiceServer) Watch(*WatchRequest, grpc.ServerStreamingServer[Quote]) error {
	return status.Errorf(codes.Unimplemented, "method Watch not implemented")
}
func (UnimplementedQuoteServiceServer) Subscribe(grpc.BidiStreamingServer[SubscribeRequest, Quote]) error {
	return status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}
func (UnimplementedQuoteServiceServer) mustEmbedUnimplementedQuoteServiceServer() {}
func (UnimplementedQuoteServiceServer) testEmbeddedByValue()                      {}

// UnsafeQuoteServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QuoteServiceServer will
// result in compilation errors.
type UnsafeQuoteServiceServer interface {
	mustEmbedUnimplementedQuoteServiceServer()
}

func RegisterQuoteServiceServer(s grpc.ServiceRegistrar, srv QuoteServiceServer) {
	// If the following call pancis, it indicates UnimplementedQuoteServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&QuoteService_ServiceDesc, srv)
}

func _QuoteService_GetQuote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuoteServiceServer).GetQuote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuoteService_GetQuote_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuoteServiceServer).GetQuote(ctx, req.(*GetQuoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuoteService_Watch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(QuoteServiceServer).Watch(m, &grpc.GenericServerStream[WatchRequest, Quote]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type QuoteService_WatchServer = grpc.ServerStreamingServer[Quote]

func _QuoteService_Subscribe_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(QuoteServiceServer).Subscribe(&grpc.GenericServerStream[SubscribeRequest, Quote]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type QuoteService_SubscribeServer = grpc.BidiStreamingServer[SubscribeRequest, Quote]

// QuoteService_ServiceDesc is the grpc.ServiceDesc for QuoteService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var QuoteService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "quote.QuoteService",
	HandlerType: (*QuoteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetQuote",
			Handler:    _QuoteService_GetQuote_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Watch",
			Handler:       _QuoteService_Watch_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Subscribe",
			Handler:       _QuoteService_Subscribe_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "proto/quote.proto",
}
