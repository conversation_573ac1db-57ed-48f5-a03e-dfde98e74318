syntax = "proto3";

package quote;

option go_package = "./proto/quote";

import "google/protobuf/timestamp.proto";

// Quote service definition
service QuoteService {
  // Unary RPC: Get current quote for a symbol
  rpc GetQuote(GetQuoteRequest) returns (Quote);
  
  // Server streaming RPC: Watch price updates for a symbol
  rpc Watch(WatchRequest) returns (stream Quote);
  
  // Bidirectional streaming RPC: Subscribe/unsubscribe to multiple symbols
  rpc Subscribe(stream SubscribeRequest) returns (stream Quote);
}

// Request message for GetQuote
message GetQuoteRequest {
  string symbol = 1;
}

// Request message for Watch
message WatchRequest {
  string symbol = 1;
}

// Request message for Subscribe stream
message SubscribeRequest {
  enum Action {
    SUBSCRIBE = 0;
    UNSUBSCRIBE = 1;
  }
  
  Action action = 1;
  string symbol = 2;
}

// Quote message
message Quote {
  string symbol = 1;
  double price = 2;
  google.protobuf.Timestamp timestamp = 3;
  int64 volume = 4;
}
