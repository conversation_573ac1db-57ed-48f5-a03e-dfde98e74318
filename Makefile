.PHONY: build run test clean proto

# Build the application
build:
	go build -o bin/grpcservice cmd/grpcservice/main.go

# Run the application
run:
	go run cmd/grpcservice/main.go

# Run with custom API key
run-with-key:
	API_KEY=my-secret-key go run cmd/grpcservice/main.go

# Run tests
test:
	go test ./...

# Run tests with coverage
test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Generate protobuf code
proto:
	protoc --go_out=. --go-grpc_out=. proto/quote.proto

# Clean build artifacts
clean:
	rm -rf bin/
	rm -f coverage.out coverage.html

# Install dependencies
deps:
	go mod tidy
	go mod download

# Install protoc plugins
install-proto:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Example grpcurl commands
examples:
	@echo "Health check:"
	@echo "grpcurl -plaintext localhost:50051 grpc.health.v1.Health/Check"
	@echo ""
	@echo "Get quote:"
	@echo "grpcurl -plaintext -H 'authorization: Bearer default-api-key-for-demo' -d '{\"symbol\":\"AAPL\"}' localhost:50051 quote.QuoteService/GetQuote"
	@echo ""
	@echo "Watch quotes:"
	@echo "grpcurl -plaintext -H 'authorization: Bearer default-api-key-for-demo' -d '{\"symbol\":\"AAPL\"}' localhost:50051 quote.QuoteService/Watch"
