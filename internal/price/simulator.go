package price

import (
	"context"
	"math"
	"math/rand"
	"sync"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	pb "quote-stream/api/proto/quote"
)

type PriceUpdate struct {
	Symbol string
	Price  float64
	Volume int64
}

type Simulator struct {
	mu          sync.RWMutex
	prices      map[string]float64
	subscribers map[string]map[chan *pb.Quote]bool // symbol -> channels
	running     bool
	ctx         context.Context
	cancel      context.CancelFunc
}

func NewSimulator() *Simulator {
	ctx, cancel := context.WithCancel(context.Background())

	sim := &Simulator{
		prices:      make(map[string]float64),
		subscribers: make(map[string]map[chan *pb.Quote]bool),
		ctx:         ctx,
		cancel:      cancel,
	}

	// Initialize some default symbols with base prices
	sim.prices["AAPL"] = 150.0
	sim.prices["GOOGL"] = 2500.0
	sim.prices["TSLA"] = 800.0
	sim.prices["MSFT"] = 300.0
	sim.prices["AMZN"] = 3200.0

	return sim
}

func (s *Simulator) Start() {
	s.mu.Lock()
	if s.running {
		s.mu.Unlock()
		return
	}
	s.running = true
	s.mu.Unlock()

	go s.simulate()
}

func (s *Simulator) Stop() {
	s.cancel()
	s.mu.Lock()
	s.running = false
	s.mu.Unlock()
}

func (s *Simulator) GetPrice(symbol string) (float64, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	price, exists := s.prices[symbol]
	return price, exists
}

func (s *Simulator) Subscribe(symbol string) chan *pb.Quote {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Ensure symbol exists
	if _, exists := s.prices[symbol]; !exists {
		s.prices[symbol] = 100.0 + rand.Float64()*900.0 // Random price between 100-1000
	}

	// Create subscriber channel
	ch := make(chan *pb.Quote, 10)

	if s.subscribers[symbol] == nil {
		s.subscribers[symbol] = make(map[chan *pb.Quote]bool)
	}
	s.subscribers[symbol][ch] = true

	return ch
}

func (s *Simulator) Unsubscribe(symbol string, ch chan *pb.Quote) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if subscribers, exists := s.subscribers[symbol]; exists {
		delete(subscribers, ch)
		close(ch)

		// Clean up empty symbol subscriptions
		if len(subscribers) == 0 {
			delete(s.subscribers, symbol)
		}
	}
}

func (s *Simulator) simulate() {
	ticker := time.NewTicker(200 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.updatePrices()
		}
	}
}

func (s *Simulator) updatePrices() {
	s.mu.Lock()
	defer s.mu.Unlock()

	for symbol, currentPrice := range s.prices {
		// Gaussian random walk with small volatility
		change := rand.NormFloat64() * 0.01 * currentPrice // 1% volatility
		newPrice := math.Max(0.01, currentPrice+change)    // Ensure price stays positive
		s.prices[symbol] = newPrice

		// Create quote message
		quote := &pb.Quote{
			Symbol:    symbol,
			Price:     newPrice,
			Timestamp: timestamppb.Now(),
			Volume:    rand.Int63n(10000) + 1000, // Random volume between 1000-11000
		}

		// Broadcast to all subscribers of this symbol
		if subscribers, exists := s.subscribers[symbol]; exists {
			for ch := range subscribers {
				select {
				case ch <- quote:
				default:
					// Channel is full, skip this update
				}
			}
		}
	}
}

func (s *Simulator) GetQuote(symbol string) *pb.Quote {
	s.mu.RLock()
	defer s.mu.RUnlock()

	price, exists := s.prices[symbol]
	if !exists {
		return nil
	}

	return &pb.Quote{
		Symbol:    symbol,
		Price:     price,
		Timestamp: timestamppb.Now(),
		Volume:    rand.Int63n(10000) + 1000,
	}
}
