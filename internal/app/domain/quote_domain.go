package domain

import (
	"context"
	pb "quote-stream/api/proto/quote"
)

// QuoteUsecase represents the quote's usecases
type QuoteUsecase interface {
	GetQuote(ctx context.Context, symbol string) (*pb.Quote, error)
	WatchQuote(ctx context.Context, symbol string) (<-chan *pb.Quote, error)
	Subscribe(ctx context.Context, symbol string) (<-chan *pb.Quote, error)
	Unsubscribe(symbol string, ch <-chan *pb.Quote) error
}

// PriceSimulator represents the price simulation interface
type PriceSimulator interface {
	Start()
	Stop()
	GetPrice(symbol string) (float64, bool)
	GetQuote(symbol string) *pb.Quote
	Subscribe(symbol string) chan *pb.Quote
	Unsubscribe(symbol string, ch chan *pb.Quote)
}

// RateLimiter represents rate limiting interface
type RateLimiter interface {
	Allow(key string) bool
}

// AuthService represents authentication interface
type AuthService interface {
	ValidateAPIKey(apiKey string) bool
}
