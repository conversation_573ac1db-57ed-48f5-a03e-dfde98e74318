package middleware

import (
	"context"
	"os"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

type AuthService struct {
	apiKey string
}

func NewAuthService() *AuthService {
	apiKey := os.Getenv("API_KEY")
	if apiKey == "" {
		apiKey = "default-api-key-for-demo" // Default for demo purposes
	}
	return &AuthService{
		apiKey: apiKey,
	}
}

func (a *AuthService) ValidateAPIKey(apiKey string) bool {
	return apiKey == a.apiKey
}

// UnaryAuthInterceptor provides authentication for unary RPCs
func (a *AuthService) UnaryAuthInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Skip auth for health check and reflection
		if strings.Contains(info.FullMethod, "grpc.health") || strings.Contains(info.FullMethod, "grpc.reflection") {
			return handler(ctx, req)
		}

		if err := a.authenticate(ctx); err != nil {
			return nil, err
		}

		return handler(ctx, req)
	}
}

// StreamAuthInterceptor provides authentication for streaming RPCs
func (a *AuthService) StreamAuthInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// Skip auth for health check and reflection
		if strings.Contains(info.FullMethod, "grpc.health") || strings.Contains(info.FullMethod, "grpc.reflection") {
			return handler(srv, stream)
		}

		if err := a.authenticate(stream.Context()); err != nil {
			return err
		}

		return handler(srv, stream)
	}
}

func (a *AuthService) authenticate(ctx context.Context) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return status.Error(codes.Unauthenticated, "missing metadata")
	}

	authorization := md.Get("authorization")
	if len(authorization) == 0 {
		return status.Error(codes.Unauthenticated, "missing authorization header")
	}

	token := authorization[0]
	if !strings.HasPrefix(token, "Bearer ") {
		return status.Error(codes.Unauthenticated, "invalid authorization format")
	}

	apiKey := strings.TrimPrefix(token, "Bearer ")
	if !a.ValidateAPIKey(apiKey) {
		return status.Error(codes.Unauthenticated, "invalid API key")
	}

	return nil
}
