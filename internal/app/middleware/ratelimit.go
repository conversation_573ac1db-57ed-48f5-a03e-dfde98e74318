package middleware

import (
	"context"
	"sync"
	"time"

	"golang.org/x/time/rate"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

type RateLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	rate     rate.Limit
	burst    int
}

func NewRateLimiter(rps int, burst int) *RateLimiter {
	return &RateLimiter{
		limiters: make(map[string]*rate.Limiter),
		rate:     rate.Limit(rps),
		burst:    burst,
	}
}

func (rl *RateLimiter) Allow(key string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	limiter, exists := rl.limiters[key]
	if !exists {
		limiter = rate.NewLimiter(rl.rate, rl.burst)
		rl.limiters[key] = limiter
	}

	return limiter.Allow()
}

func (rl *RateLimiter) getClientKey(ctx context.Context) string {
	// Try to get client IP or use API key as identifier
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "unknown"
	}

	// Use authorization header as key for rate limiting
	authorization := md.Get("authorization")
	if len(authorization) > 0 {
		return authorization[0]
	}

	// Fallback to a default key
	return "default"
}

// UnaryRateLimitInterceptor provides rate limiting for unary RPCs
func (rl *RateLimiter) UnaryRateLimitInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		clientKey := rl.getClientKey(ctx)
		
		if !rl.Allow(clientKey) {
			return nil, status.Error(codes.ResourceExhausted, "rate limit exceeded")
		}

		return handler(ctx, req)
	}
}

// StreamRateLimitInterceptor provides rate limiting for streaming RPCs
func (rl *RateLimiter) StreamRateLimitInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		clientKey := rl.getClientKey(stream.Context())
		
		if !rl.Allow(clientKey) {
			return status.Error(codes.ResourceExhausted, "rate limit exceeded")
		}

		return handler(srv, stream)
	}
}

// Cleanup removes old limiters to prevent memory leaks
func (rl *RateLimiter) Cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	go func() {
		for range ticker.C {
			rl.mu.Lock()
			// Simple cleanup: remove all limiters periodically
			// In production, you might want more sophisticated cleanup
			rl.limiters = make(map[string]*rate.Limiter)
			rl.mu.Unlock()
		}
	}()
}
