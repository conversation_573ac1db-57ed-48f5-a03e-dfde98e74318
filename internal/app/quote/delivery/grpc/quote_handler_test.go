package grpc

import (
	"context"
	"net"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/test/bufconn"

	pb "quote-stream/api/proto/quote"
	"quote-stream/internal/app/quote/usecase"
	"quote-stream/internal/price"
)

const bufSize = 1024 * 1024

var lis *bufconn.Listener

func init() {
	lis = bufconn.Listen(bufSize)
}

func setupTestServer(t *testing.T) (pb.QuoteServiceClient, func()) {
	// Create a new listener for each test
	testLis := bufconn.Listen(bufSize)

	// Setup simulator and usecase
	simulator := price.NewSimulator()
	simulator.Start()

	quoteUsecase := usecase.NewQuoteUsecase(simulator)
	handler := NewQuoteHandler(quoteUsecase)

	// Setup gRPC server
	server := grpc.NewServer()
	pb.RegisterQuoteServiceServer(server, handler)

	go func() {
		if err := server.Serve(testLis); err != nil {
			t.Logf("Server exited with error: %v", err)
		}
	}()

	// Setup client
	conn, err := grpc.DialContext(
		context.Background(),
		"bufnet",
		grpc.WithContextDialer(func(context.Context, string) (net.Conn, error) {
			return testLis.Dial()
		}),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	require.NoError(t, err)

	client := pb.NewQuoteServiceClient(conn)

	cleanup := func() {
		conn.Close()
		server.Stop()
		simulator.Stop()
		testLis.Close()
	}

	return client, cleanup
}

func TestQuoteHandler_GetQuote(t *testing.T) {
	client, cleanup := setupTestServer(t)
	defer cleanup()

	tests := []struct {
		name    string
		symbol  string
		wantErr bool
	}{
		{
			name:    "valid symbol",
			symbol:  "AAPL",
			wantErr: false,
		},
		{
			name:    "empty symbol",
			symbol:  "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			req := &pb.GetQuoteRequest{Symbol: tt.symbol}

			resp, err := client.GetQuote(ctx, req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.symbol, resp.Symbol)
				assert.Greater(t, resp.Price, 0.0)
			}
		})
	}
}

func TestQuoteHandler_Watch_Cancellation(t *testing.T) {
	client, cleanup := setupTestServer(t)
	defer cleanup()

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	req := &pb.WatchRequest{Symbol: "AAPL"}
	stream, err := client.Watch(ctx, req)
	require.NoError(t, err)

	// Receive at least one quote
	quote, err := stream.Recv()
	assert.NoError(t, err)
	assert.NotNil(t, quote)
	assert.Equal(t, "AAPL", quote.Symbol)

	// Cancel the context
	cancel()

	// Next receive should fail due to cancellation
	_, err = stream.Recv()
	assert.Error(t, err)
}

func TestQuoteHandler_Watch_StreamingUpdates(t *testing.T) {
	client, cleanup := setupTestServer(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	req := &pb.WatchRequest{Symbol: "AAPL"}
	stream, err := client.Watch(ctx, req)
	require.NoError(t, err)

	// Receive multiple quotes
	quotesReceived := 0
	for quotesReceived < 3 {
		quote, err := stream.Recv()
		if err != nil {
			break
		}

		assert.NotNil(t, quote)
		assert.Equal(t, "AAPL", quote.Symbol)
		assert.Greater(t, quote.Price, 0.0)
		quotesReceived++
	}

	assert.GreaterOrEqual(t, quotesReceived, 1, "Should receive at least one quote")
}
