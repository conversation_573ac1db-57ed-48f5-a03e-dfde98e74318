package grpc

import (
	"context"
	"io"
	"log"
	"sync"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pb "quote-stream/api/proto/quote"
	"quote-stream/internal/app/domain"
)

type QuoteHandler struct {
	pb.UnimplementedQuoteServiceServer
	quoteUsecase domain.QuoteUsecase
}

func NewQuoteHandler(quoteUsecase domain.QuoteUsecase) *QuoteHandler {
	return &QuoteHandler{
		quoteUsecase: quoteUsecase,
	}
}

// GetQuote implements unary RPC
func (h *QuoteHandler) GetQuote(ctx context.Context, req *pb.GetQuoteRequest) (*pb.Quote, error) {
	if req.Symbol == "" {
		return nil, status.Error(codes.InvalidArgument, "symbol is required")
	}

	// Check for context cancellation
	select {
	case <-ctx.Done():
		return nil, status.Error(codes.Canceled, "request cancelled")
	default:
	}

	quote, err := h.quoteUsecase.GetQuote(ctx, req.Symbol)
	if err != nil {
		log.Printf("GetQuote error: %v", err)
		return nil, status.Error(codes.NotFound, err.Error())
	}

	return quote, nil
}

// Watch implements server streaming RPC
func (h *QuoteHandler) Watch(req *pb.WatchRequest, stream pb.QuoteService_WatchServer) error {
	if req.Symbol == "" {
		return status.Error(codes.InvalidArgument, "symbol is required")
	}

	ctx := stream.Context()
	quoteCh, err := h.quoteUsecase.WatchQuote(ctx, req.Symbol)
	if err != nil {
		log.Printf("Watch error: %v", err)
		return status.Error(codes.Internal, err.Error())
	}

	log.Printf("Starting watch for symbol: %s", req.Symbol)

	for {
		select {
		case <-ctx.Done():
			log.Printf("Watch cancelled for symbol: %s", req.Symbol)
			return status.Error(codes.Canceled, "watch cancelled")
		case quote, ok := <-quoteCh:
			if !ok {
				log.Printf("Quote channel closed for symbol: %s", req.Symbol)
				return nil
			}

			if err := stream.Send(quote); err != nil {
				log.Printf("Failed to send quote for %s: %v", req.Symbol, err)
				return err
			}
		}
	}
}

// Subscribe implements bidirectional streaming RPC
func (h *QuoteHandler) Subscribe(stream pb.QuoteService_SubscribeServer) error {
	ctx := stream.Context()

	// Track active subscriptions for this client
	subscriptions := make(map[string]<-chan *pb.Quote)
	var mu sync.RWMutex

	// Channel to coordinate shutdown
	done := make(chan struct{})

	// Goroutine to handle incoming subscription requests
	go func() {
		defer close(done)

		for {
			req, err := stream.Recv()
			if err == io.EOF {
				log.Println("Client closed subscription stream")
				return
			}
			if err != nil {
				log.Printf("Error receiving subscription request: %v", err)
				return
			}

			mu.Lock()
			switch req.Action {
			case pb.SubscribeRequest_SUBSCRIBE:
				if req.Symbol == "" {
					mu.Unlock()
					continue
				}

				// Check if already subscribed
				if _, exists := subscriptions[req.Symbol]; exists {
					mu.Unlock()
					continue
				}

				quoteCh, err := h.quoteUsecase.Subscribe(ctx, req.Symbol)
				if err != nil {
					log.Printf("Failed to subscribe to %s: %v", req.Symbol, err)
					mu.Unlock()
					continue
				}

				subscriptions[req.Symbol] = quoteCh
				log.Printf("Subscribed to symbol: %s", req.Symbol)

			case pb.SubscribeRequest_UNSUBSCRIBE:
				if quoteCh, exists := subscriptions[req.Symbol]; exists {
					h.quoteUsecase.Unsubscribe(req.Symbol, quoteCh)
					delete(subscriptions, req.Symbol)
					log.Printf("Unsubscribed from symbol: %s", req.Symbol)
				}
			}
			mu.Unlock()
		}
	}()

	// Goroutine to handle outgoing quote updates
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-done:
				return
			default:
				mu.RLock()
				for symbol, quoteCh := range subscriptions {
					select {
					case quote, ok := <-quoteCh:
						if !ok {
							// Channel closed, remove subscription
							delete(subscriptions, symbol)
							continue
						}

						if err := stream.Send(quote); err != nil {
							log.Printf("Failed to send quote for %s: %v", symbol, err)
							mu.RUnlock()
							return
						}
					default:
						// No quote available, continue
					}
				}
				mu.RUnlock()
			}
		}
	}()

	// Wait for completion
	select {
	case <-ctx.Done():
		log.Println("Subscribe stream cancelled")
		return status.Error(codes.Canceled, "subscription cancelled")
	case <-done:
		log.Println("Subscribe stream completed")
		return nil
	}
}
