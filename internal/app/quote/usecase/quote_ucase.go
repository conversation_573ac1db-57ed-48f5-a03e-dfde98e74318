package usecase

import (
	"context"
	"errors"
	"sync"

	pb "quote-stream/api/proto/quote"
	"quote-stream/internal/app/domain"
)

type quoteUsecase struct {
	simulator domain.PriceSimulator
	mu        sync.RWMutex
	// Track active subscriptions for cleanup
	subscriptions map[string]map[<-chan *pb.Quote]chan *pb.Quote
}

func NewQuoteUsecase(simulator domain.PriceSimulator) domain.QuoteUsecase {
	return &quoteUsecase{
		simulator:     simulator,
		subscriptions: make(map[string]map[<-chan *pb.Quote]chan *pb.Quote),
	}
}

func (q *quoteUsecase) GetQuote(ctx context.Context, symbol string) (*pb.Quote, error) {
	if symbol == "" {
		return nil, errors.New("symbol cannot be empty")
	}

	// Check if context is cancelled
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	quote := q.simulator.GetQuote(symbol)
	if quote == nil {
		return nil, errors.New("symbol not found")
	}

	return quote, nil
}

func (q *quoteUsecase) WatchQuote(ctx context.Context, symbol string) (<-chan *pb.Quote, error) {
	if symbol == "" {
		return nil, errors.New("symbol cannot be empty")
	}

	// Subscribe to price updates
	simCh := q.simulator.Subscribe(symbol)

	// Create a buffered channel for the client
	clientCh := make(chan *pb.Quote, 10)

	// Start a goroutine to forward updates and handle context cancellation
	go func() {
		defer close(clientCh)
		defer q.simulator.Unsubscribe(symbol, simCh)

		for {
			select {
			case <-ctx.Done():
				return
			case quote, ok := <-simCh:
				if !ok {
					return
				}
				select {
				case clientCh <- quote:
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	return clientCh, nil
}

func (q *quoteUsecase) Subscribe(ctx context.Context, symbol string) (<-chan *pb.Quote, error) {
	if symbol == "" {
		return nil, errors.New("symbol cannot be empty")
	}

	q.mu.Lock()
	defer q.mu.Unlock()

	// Subscribe to price updates from simulator
	simCh := q.simulator.Subscribe(symbol)

	// Create a buffered channel for the client
	clientCh := make(chan *pb.Quote, 10)

	// Track the subscription
	if q.subscriptions[symbol] == nil {
		q.subscriptions[symbol] = make(map[<-chan *pb.Quote]chan *pb.Quote)
	}
	q.subscriptions[symbol][clientCh] = simCh

	// Start forwarding updates
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case quote, ok := <-simCh:
				if !ok {
					return
				}
				select {
				case clientCh <- quote:
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	return clientCh, nil
}

func (q *quoteUsecase) Unsubscribe(symbol string, ch <-chan *pb.Quote) error {
	q.mu.Lock()
	defer q.mu.Unlock()

	if subscriptions, exists := q.subscriptions[symbol]; exists {
		if simCh, exists := subscriptions[ch]; exists {
			// Unsubscribe from simulator
			q.simulator.Unsubscribe(symbol, simCh)

			// Remove from tracking
			delete(subscriptions, ch)

			// Clean up empty symbol subscriptions
			if len(subscriptions) == 0 {
				delete(q.subscriptions, symbol)
			}

			return nil
		}
	}

	return errors.New("subscription not found")
}
