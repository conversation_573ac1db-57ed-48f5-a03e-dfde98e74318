package usecase

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"quote-stream/internal/price"
)

func TestQuoteUsecase_GetQuote(t *testing.T) {
	// Setup
	simulator := price.NewSimulator()
	simulator.Start()
	defer simulator.Stop()

	usecase := NewQuoteUsecase(simulator)

	tests := []struct {
		name    string
		symbol  string
		wantErr bool
	}{
		{
			name:    "valid symbol",
			symbol:  "AAPL",
			wantErr: false,
		},
		{
			name:    "empty symbol",
			symbol:  "",
			wantErr: true,
		},
		{
			name:    "new symbol",
			symbol:  "NEWSTOCK",
			wantErr: true, // Will be nil from simulator but usecase should handle
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			quote, err := usecase.GetQuote(ctx, tt.symbol)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, quote)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, quote)
				assert.Equal(t, tt.symbol, quote.Symbol)
				assert.Greater(t, quote.Price, 0.0)
			}
		})
	}
}

func TestQuoteUsecase_GetQuote_ContextCancellation(t *testing.T) {
	// Setup
	simulator := price.NewSimulator()
	simulator.Start()
	defer simulator.Stop()

	usecase := NewQuoteUsecase(simulator)

	// Create a cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	quote, err := usecase.GetQuote(ctx, "AAPL")

	assert.Error(t, err)
	assert.Nil(t, quote)
	assert.Equal(t, context.Canceled, err)
}

func TestQuoteUsecase_WatchQuote(t *testing.T) {
	// Setup
	simulator := price.NewSimulator()
	simulator.Start()
	defer simulator.Stop()

	usecase := NewQuoteUsecase(simulator)

	t.Run("valid watch", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		quoteCh, err := usecase.WatchQuote(ctx, "AAPL")
		assert.NoError(t, err)
		assert.NotNil(t, quoteCh)

		// Should receive at least one quote
		select {
		case quote := <-quoteCh:
			assert.NotNil(t, quote)
			assert.Equal(t, "AAPL", quote.Symbol)
		case <-time.After(1 * time.Second):
			t.Fatal("Expected to receive a quote within 1 second")
		}
	})

	t.Run("empty symbol", func(t *testing.T) {
		ctx := context.Background()
		quoteCh, err := usecase.WatchQuote(ctx, "")
		assert.Error(t, err)
		assert.Nil(t, quoteCh)
	})
}

func TestQuoteUsecase_WatchQuote_ContextCancellation(t *testing.T) {
	// Setup
	simulator := price.NewSimulator()
	simulator.Start()
	defer simulator.Stop()

	usecase := NewQuoteUsecase(simulator)

	ctx, cancel := context.WithCancel(context.Background())

	quoteCh, err := usecase.WatchQuote(ctx, "AAPL")
	assert.NoError(t, err)
	assert.NotNil(t, quoteCh)

	// Cancel the context
	cancel()

	// Channel should be closed
	select {
	case _, ok := <-quoteCh:
		if ok {
			// Might receive one more quote before closure
			select {
			case _, ok := <-quoteCh:
				assert.False(t, ok, "Channel should be closed after context cancellation")
			case <-time.After(500 * time.Millisecond):
				t.Fatal("Channel should be closed within 500ms")
			}
		}
	case <-time.After(500 * time.Millisecond):
		t.Fatal("Expected channel to be closed within 500ms")
	}
}
