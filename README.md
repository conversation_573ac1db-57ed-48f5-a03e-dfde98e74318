# Quote Stream gRPC Service

一個即時報價 gRPC 服務，展示三種 RPC 型態，包含認證、限流和觀測性功能。

## 功能特色

- **三種 RPC 型態**：
  - `GetQuote` (Unary): 獲取當前報價
  - `Watch` (Server Streaming): 持續推送價格更新
  - `Subscribe` (Bidirectional Streaming): 動態訂閱/退訂多檔股票

- **安全性**：
  - API Key 認證 (Bearer Token)
  - Token Bucket Rate Limiting

- **觀測性**：
  - Health Check (grpc_health_v1)
  - gRPC Reflection
  - 結構化日誌

## 快速開始

### 1. 啟動服務

```bash
# 使用預設設定
go run cmd/grpcservice/main.go

# 自訂參數
go run cmd/grpcservice/main.go --addr=:50051 --insecure

# 設定 API Key
API_KEY=your-secret-key go run cmd/grpcservice/main.go
```

### 2. 使用 grpcurl 測試

```bash
# 健康檢查
grpcurl -plaintext localhost:50051 grpc.health.v1.Health/Check

# 獲取報價 (需要認證)
grpcurl -plaintext \
  -H 'authorization: Bearer default-api-key-for-demo' \
  -d '{"symbol":"AAPL"}' \
  localhost:50051 quote.QuoteService/GetQuote

# 監看報價更新
grpcurl -plaintext \
  -H 'authorization: Bearer default-api-key-for-demo' \
  -d '{"symbol":"AAPL"}' \
  localhost:50051 quote.QuoteService/Watch

# 雙向串流訂閱
grpcurl -plaintext \
  -H 'authorization: Bearer default-api-key-for-demo' \
  localhost:50051 quote.QuoteService/Subscribe
# 然後輸入：
# {"action": "SUBSCRIBE", "symbol": "AAPL"}
# {"action": "SUBSCRIBE", "symbol": "GOOGL"}
# {"action": "UNSUBSCRIBE", "symbol": "AAPL"}
```

### 3. 可用股票代碼

- AAPL (Apple)
- GOOGL (Google)
- TSLA (Tesla)
- MSFT (Microsoft)
- AMZN (Amazon)

## 測試

```bash
# 執行所有測試
go test ./...

# 執行特定測試
go test ./internal/app/quote/usecase/
go test ./internal/app/quote/delivery/grpc/
```

## 架構

```
cmd/grpcservice/          # 服務入口
internal/
├── app/
│   ├── domain/          # 領域接口定義
│   ├── middleware/      # 認證和限流中間件
│   └── quote/
│       ├── delivery/grpc/  # gRPC handlers
│       └── usecase/        # 業務邏輯
├── price/               # 價格模擬器
proto/                   # Protocol Buffers 定義
```

## 設計重點

- **Clean Architecture**: 分層架構，依賴反轉
- **Context 處理**: 支援取消、超時、截止時間
- **Graceful Shutdown**: 優雅關閉服務
- **Error Handling**: 適當的 gRPC 錯誤碼
- **Memory Safety**: 避免 goroutine 洩漏
