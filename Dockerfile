# Build stage
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o grpcservice cmd/grpcservice/main.go

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/grpcservice .

# Expose port
EXPOSE 50051

# Set default environment variables
ENV API_KEY=default-api-key-for-demo

# Run the binary
CMD ["./grpcservice", "--addr=:50051", "--insecure"]
