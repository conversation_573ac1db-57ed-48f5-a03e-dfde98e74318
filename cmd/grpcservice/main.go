package main

import (
	"flag"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthpb "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	pb "quote-stream/api/proto/quote"
	"quote-stream/internal/app/middleware"
	grpchandler "quote-stream/internal/app/quote/delivery/grpc"
	"quote-stream/internal/app/quote/usecase"
	"quote-stream/internal/price"
)

func main() {
	// Parse command line flags
	var (
		addr     = flag.String("addr", ":50051", "server address")
		insecure = flag.Bool("insecure", false, "run server without TLS")
	)
	flag.Parse()

	// Initialize price simulator
	simulator := price.NewSimulator()
	simulator.Start()
	defer simulator.Stop()

	// Initialize usecase
	quoteUsecase := usecase.NewQuoteUsecase(simulator)

	// Initialize middleware
	authService := middleware.NewAuthService()
	rateLimiter := middleware.NewRateLimiter(10, 20) // 10 RPS, burst of 20
	rateLimiter.Cleanup()

	// Create gRPC server with interceptors
	server := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			authService.UnaryAuthInterceptor(),
			rateLimiter.UnaryRateLimitInterceptor(),
		),
		grpc.ChainStreamInterceptor(
			authService.StreamAuthInterceptor(),
			rateLimiter.StreamRateLimitInterceptor(),
		),
	)

	// Register services
	quoteHandler := grpchandler.NewQuoteHandler(quoteUsecase)
	pb.RegisterQuoteServiceServer(server, quoteHandler)

	// Register health service
	healthServer := health.NewServer()
	healthpb.RegisterHealthServer(server, healthServer)
	healthServer.SetServingStatus("", healthpb.HealthCheckResponse_SERVING)
	healthServer.SetServingStatus("quote.QuoteService", healthpb.HealthCheckResponse_SERVING)

	// Register reflection service
	reflection.Register(server)

	// Create listener
	listener, err := net.Listen("tcp", *addr)
	if err != nil {
		log.Fatalf("Failed to listen: %v", err)
	}

	// Setup graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Shutting down gRPC server...")
		server.GracefulStop()
	}()

	// Print startup information
	fmt.Printf("Quote Stream gRPC Server starting on %s\n", *addr)
	fmt.Printf("Insecure mode: %v\n", *insecure)
	fmt.Printf("API Key: %s\n", getAPIKey())
	fmt.Println("Available symbols: AAPL, GOOGL, TSLA, MSFT, AMZN")
	fmt.Println("\nExample grpcurl commands:")
	fmt.Printf("# Health check:\n")
	fmt.Printf("grpcurl -plaintext %s grpc.health.v1.Health/Check\n\n", *addr)
	fmt.Printf("# Get quote:\n")
	fmt.Printf("grpcurl -plaintext -H 'authorization: Bearer %s' -d '{\"symbol\":\"AAPL\"}' %s quote.QuoteService/GetQuote\n\n", getAPIKey(), *addr)
	fmt.Printf("# Watch quotes:\n")
	fmt.Printf("grpcurl -plaintext -H 'authorization: Bearer %s' -d '{\"symbol\":\"AAPL\"}' %s quote.QuoteService/Watch\n\n", getAPIKey(), *addr)

	// Start server
	log.Printf("Server listening at %v", listener.Addr())
	if err := server.Serve(listener); err != nil {
		log.Fatalf("Failed to serve: %v", err)
	}
}

func getAPIKey() string {
	apiKey := os.Getenv("API_KEY")
	if apiKey == "" {
		return "default-api-key-for-demo"
	}
	return apiKey
}
