package main

import (
	"context"
	"flag"
	"fmt"
	"io"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	pb "quote-stream/api/proto/quote"
)

func main() {
	var (
		addr   = flag.String("addr", "localhost:50051", "server address")
		apiKey = flag.String("key", "default-api-key-for-demo", "API key")
		symbol = flag.String("symbol", "AAPL", "stock symbol")
		mode   = flag.String("mode", "get", "mode: get, watch, subscribe")
	)
	flag.Parse()

	// Connect to server
	conn, err := grpc.NewClient(*addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer conn.Close()

	client := pb.NewQuoteServiceClient(conn)

	// Create context with auth
	ctx := metadata.AppendToOutgoingContext(context.Background(), "authorization", "Bearer "+*apiKey)

	switch *mode {
	case "get":
		testGetQuote(ctx, client, *symbol)
	case "watch":
		testWatch(ctx, client, *symbol)
	case "subscribe":
		testSubscribe(ctx, client)
	default:
		fmt.Println("Invalid mode. Use: get, watch, subscribe")
	}
}

func testGetQuote(ctx context.Context, client pb.QuoteServiceClient, symbol string) {
	fmt.Printf("Getting quote for %s...\n", symbol)

	resp, err := client.GetQuote(ctx, &pb.GetQuoteRequest{Symbol: symbol})
	if err != nil {
		log.Fatalf("GetQuote failed: %v", err)
	}

	fmt.Printf("Quote: %s = $%.2f (Volume: %d) at %s\n",
		resp.Symbol, resp.Price, resp.Volume, resp.Timestamp.AsTime().Format(time.RFC3339))
}

func testWatch(ctx context.Context, client pb.QuoteServiceClient, symbol string) {
	fmt.Printf("Watching quotes for %s (press Ctrl+C to stop)...\n", symbol)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	stream, err := client.Watch(ctx, &pb.WatchRequest{Symbol: symbol})
	if err != nil {
		log.Fatalf("Watch failed: %v", err)
	}

	for {
		quote, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Fatalf("Watch error: %v", err)
		}

		fmt.Printf("Update: %s = $%.2f (Volume: %d) at %s\n",
			quote.Symbol, quote.Price, quote.Volume, quote.Timestamp.AsTime().Format("15:04:05"))
	}
}

func testSubscribe(ctx context.Context, client pb.QuoteServiceClient) {
	fmt.Println("Testing bidirectional streaming...")

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	stream, err := client.Subscribe(ctx)
	if err != nil {
		log.Fatalf("Subscribe failed: %v", err)
	}

	// Subscribe to multiple symbols
	symbols := []string{"AAPL", "GOOGL", "TSLA"}

	go func() {
		defer stream.CloseSend()

		// Subscribe to symbols
		for _, symbol := range symbols {
			fmt.Printf("Subscribing to %s...\n", symbol)
			err := stream.Send(&pb.SubscribeRequest{
				Action: pb.SubscribeRequest_SUBSCRIBE,
				Symbol: symbol,
			})
			if err != nil {
				log.Printf("Failed to subscribe to %s: %v", symbol, err)
				return
			}
			time.Sleep(1 * time.Second)
		}

		// Wait a bit, then unsubscribe from one
		time.Sleep(5 * time.Second)
		fmt.Printf("Unsubscribing from %s...\n", symbols[0])
		err := stream.Send(&pb.SubscribeRequest{
			Action: pb.SubscribeRequest_UNSUBSCRIBE,
			Symbol: symbols[0],
		})
		if err != nil {
			log.Printf("Failed to unsubscribe from %s: %v", symbols[0], err)
		}
	}()

	// Receive updates
	for {
		quote, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Subscribe error: %v", err)
			break
		}

		fmt.Printf("Received: %s = $%.2f (Volume: %d) at %s\n",
			quote.Symbol, quote.Price, quote.Volume, quote.Timestamp.AsTime().Format("15:04:05"))
	}
}
